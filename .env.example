# API Configuration
# Para desarrollo local, usar el servidor de desarrollo
VITE_API_BASE_URL=http://localhost:3001/api
# Para producción, usar la URL de API Gateway
# VITE_API_BASE_URL=https://your-api-gateway-url.execute-api.region.amazonaws.com/prod

# AWS Configuration (para scripts de despliegue)
AWS_REGION=us-east-1
AWS_PROFILE=default

# S3 Bucket para despliegue estático
S3_BUCKET_NAME=mini-reddit-static-site

# CloudFront Distribution ID (se genera después del primer despliegue)
CLOUDFRONT_DISTRIBUTION_ID=

# Amplify App ID (se genera después de crear la app)
AMPLIFY_APP_ID=

# DynamoDB Table Names
DYNAMODB_POSTS_TABLE=mini-reddit-posts
DYNAMODB_COMMENTS_TABLE=mini-reddit-comments
DYNAMODB_VOTES_TABLE=mini-reddit-votes
DYNAMODB_USERS_TABLE=mini-reddit-users

# Lambda Function Names
LAMBDA_POSTS_FUNCTION=mini-reddit-posts-handler
LAMBDA_COMMENTS_FUNCTION=mini-reddit-comments-handler
LAMBDA_VOTES_FUNCTION=mini-reddit-votes-handler
LAMBDA_USERS_FUNCTION=mini-reddit-users-handler
