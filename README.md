# Mini Reddit - Red Social Universitaria

Una red social tipo Reddit/Twitter desarrollada como proyecto universitario, construida con React + AWS Lambda + DynamoDB.

## 🚀 Características

- **Posts y comentarios**: Sistema de publicaciones con comentarios anidados
- **Sistema de votos**: Votos positivos y negativos estilo Reddit
- **Usuarios anónimos**: Identificación basada en IP con alias personalizables
- **Interfaz moderna**: Diseño responsive con TailwindCSS
- **Backend serverless**: AWS Lambda + DynamoDB (capa gratuita)
- **Despliegue automático**: Scripts para S3+CloudFront y Amplify

## 🛠️ Stack Tecnológico

### Frontend
- **React 18** - Biblioteca de UI
- **Vite** - Herramienta de desarrollo
- **TailwindCSS** - Framework de CSS
- **React Router** - Navegación SPA
- **Axios** - Cliente HTTP
- **Lucide React** - Iconos

### Backend
- **AWS Lambda** - Funciones serverless
- **API Gateway** - API REST
- **DynamoDB** - Base de datos NoSQL
- **Node.js 18** - Runtime

### Despliegue
- **AWS S3** - Hosting estático
- **CloudFront** - CDN
- **AWS Amplify** - Despliegue automatizado

## 📁 Estructura del Proyecto

```
mini-reddit/
├── src/                    # Frontend React
│   ├── components/         # Componentes reutilizables
│   ├── pages/             # Páginas principales
│   ├── contexts/          # Context API (usuario)
│   └── utils/             # Utilidades y API
├── backend/               # Backend AWS Lambda
│   └── src/
│       ├── handlers/      # Funciones Lambda
│       └── utils/         # Utilidades backend
├── scripts/               # Scripts de despliegue
├── docs/                  # Documentación adicional
└── public/                # Assets estáticos
```

## 🚀 Instalación y Desarrollo Local

### Prerrequisitos

- Node.js 18+ y npm
- Cuenta AWS con CLI configurado
- Git

### 1. Clonar y configurar

```bash
# Clonar el repositorio
git clone <tu-repositorio>
cd mini-reddit

# Instalar dependencias del frontend
npm install

# Instalar dependencias del backend
cd backend
npm install
cd ..
```

### 2. Configurar variables de entorno

```bash
# Copiar archivo de ejemplo
cp .env.example .env

# Editar .env con tus configuraciones
# VITE_API_BASE_URL será configurado después del despliegue
```

### 3. Desarrollo local

**Opción A: Solo frontend (datos de ejemplo)**
```bash
# Iniciar solo el frontend
npm run dev

# El sitio estará disponible en http://localhost:3000
```

**Opción B: Frontend + Backend local**
```bash
# Instalar concurrently si no está instalado
npm install

# Iniciar frontend + servidor de desarrollo
npm run dev:full

# Frontend: http://localhost:3000
# Backend: http://localhost:3001
```

**Nota**: En desarrollo local, la aplicación puede usar datos de ejemplo o un servidor local simulado.

## ☁️ Despliegue en AWS

### Opción 1: Despliegue Completo Automatizado

```bash
# 1. Configurar recursos AWS (DynamoDB + Lambda + API Gateway)
npm run aws:setup

# 2. Configurar API Gateway
node scripts/setup-api-gateway.js

# 3. Actualizar .env con la URL de API Gateway generada
# VITE_API_BASE_URL=https://tu-api-id.execute-api.region.amazonaws.com/prod

# 4. Desplegar frontend a S3 + CloudFront
npm run deploy:s3
```

### Opción 2: Despliegue con Amplify

```bash
# 1. Configurar backend (pasos 1-3 de arriba)
npm run aws:setup
node scripts/setup-api-gateway.js

# 2. Desplegar con Amplify
npm run deploy:amplify
```

## 🔧 Configuración AWS

### Permisos necesarios

Tu usuario AWS necesita permisos para:
- DynamoDB (crear tablas, leer/escribir)
- Lambda (crear funciones, actualizar código)
- API Gateway (crear APIs, configurar métodos)
- IAM (crear roles para Lambda)
- S3 (crear buckets, subir archivos)
- CloudFront (crear distribuciones)
- Amplify (crear aplicaciones)

### Costos estimados

Usando la **capa gratuita de AWS**:
- DynamoDB: 25 GB gratis
- Lambda: 1M invocaciones gratis/mes
- API Gateway: 1M llamadas gratis/mes
- S3: 5 GB gratis
- CloudFront: 50 GB gratis/mes

**Costo mensual estimado**: $0-5 USD (después de capa gratuita)

## 🎮 Uso de la Aplicación

### Para usuarios

1. **Acceder**: Visita el sitio web, se generará automáticamente un alias basado en tu IP
2. **Crear posts**: Haz clic en "Crear Post" y escribe tu contenido
3. **Comentar**: Responde a posts y otros comentarios
4. **Votar**: Usa las flechas para votar positivo/negativo
5. **Personalizar**: Cambia tu alias en el menú de usuario

### Para desarrolladores

- **API REST**: Endpoints documentados en `ARCHITECTURE.md`
- **Base de datos**: Esquemas DynamoDB en `backend/src/utils/dynamodb.js`
- **Componentes**: Componentes React modulares y reutilizables
- **Estilos**: Clases TailwindCSS personalizadas en `src/index.css`

## 📚 Documentación Adicional

- [**DEPLOY.md**](./DEPLOY.md) - Guías detalladas de despliegue
- [**ARCHITECTURE.md**](./ARCHITECTURE.md) - Arquitectura y flujo de datos
- [**USERS.md**](./USERS.md) - Sistema de usuarios y alias

## 🐛 Troubleshooting

### Problemas comunes

**Error de CORS en desarrollo**:
```bash
# Verificar que VITE_API_BASE_URL esté configurado correctamente
echo $VITE_API_BASE_URL
```

**Lambda timeout**:
- Verificar permisos IAM
- Revisar logs en CloudWatch

**DynamoDB access denied**:
- Verificar que las tablas existan
- Revisar permisos del rol Lambda

### Logs y debugging

```bash
# Ver logs de Lambda en CloudWatch
aws logs describe-log-groups --log-group-name-prefix "/aws/lambda/mini-reddit"

# Ver estado de las tablas DynamoDB
aws dynamodb list-tables
```

## 🤝 Contribuir

Este es un proyecto universitario, pero las contribuciones son bienvenidas:

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit tus cambios (`git commit -m 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver `LICENSE` para más detalles.

## 👨‍💻 Autor

Proyecto universitario desarrollado como demostración de:
- Desarrollo fullstack moderno
- Arquitectura serverless en AWS
- Buenas prácticas de programación
- Documentación técnica completa

---

**¿Necesitas ayuda?** Revisa la documentación en la carpeta `docs/` o abre un issue en GitHub.
