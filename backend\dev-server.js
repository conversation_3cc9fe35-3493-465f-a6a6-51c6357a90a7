const express = require('express');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Simulación de base de datos en memoria para desarrollo
let posts = [
  {
    id: '1',
    title: '¡Bienvenido a Mini Reddit!',
    content: 'Este es el primer post de ejemplo. Aquí puedes compartir tus ideas y discutir con otros usuarios.',
    author: 'admin',
    authorId: 'admin-id',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    votes: 5,
    commentCount: 2
  },
  {
    id: '2',
    title: 'Cómo funciona el sistema de votos',
    content: 'Puedes votar positivo o negativo en posts y comentarios. Los votos ayudan a destacar el mejor contenido.',
    author: 'moderator',
    authorId: 'mod-id',
    createdAt: new Date(Date.now() - 3600000).toISOString(),
    updatedAt: new Date(Date.now() - 3600000).toISOString(),
    votes: 3,
    commentCount: 1
  }
];

let comments = [
  {
    id: 'c1',
    postId: '1',
    content: '¡Excelente plataforma! Me gusta mucho el diseño.',
    author: 'user1',
    authorId: 'user1-id',
    parentId: null,
    createdAt: new Date(Date.now() - 1800000).toISOString(),
    votes: 2,
    replies: []
  },
  {
    id: 'c2',
    postId: '1',
    content: 'Estoy de acuerdo, muy fácil de usar.',
    author: 'user2',
    authorId: 'user2-id',
    parentId: 'c1',
    createdAt: new Date(Date.now() - 900000).toISOString(),
    votes: 1,
    replies: []
  },
  {
    id: 'c3',
    postId: '2',
    content: 'Gracias por la explicación, muy útil.',
    author: 'user3',
    authorId: 'user3-id',
    parentId: null,
    createdAt: new Date(Date.now() - 600000).toISOString(),
    votes: 1,
    replies: []
  }
];

let users = {};
let votes = {};

// Función para generar alias basado en IP
function generateAlias(ip) {
  const adjectives = ['Clever', 'Bright', 'Swift', 'Bold', 'Wise', 'Cool', 'Smart', 'Quick'];
  const nouns = ['Panda', 'Tiger', 'Eagle', 'Wolf', 'Fox', 'Bear', 'Lion', 'Hawk'];
  const adj = adjectives[Math.floor(Math.random() * adjectives.length)];
  const noun = nouns[Math.floor(Math.random() * nouns.length)];
  const num = Math.floor(Math.random() * 1000);
  return `${adj}${noun}${num}`;
}

// Función para obtener o crear usuario
function getOrCreateUser(req) {
  const ip = req.ip || req.connection.remoteAddress || '127.0.0.1';
  const userAgent = req.get('User-Agent') || '';
  const userId = req.headers['x-user-id'];
  const userAlias = req.headers['x-user-alias'];
  
  if (userId && users[userId]) {
    return users[userId];
  }
  
  const newUserId = userId || uuidv4();
  const alias = userAlias || generateAlias(ip);
  
  users[newUserId] = {
    id: newUserId,
    alias: alias,
    ip: ip,
    userAgent: userAgent,
    createdAt: new Date().toISOString()
  };
  
  return users[newUserId];
}

// ===== ROUTES =====

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Mini Reddit Dev Server is running' });
});

// ===== POSTS ROUTES =====

// GET /api/posts - Obtener todos los posts
app.get('/api/posts', (req, res) => {
  const limit = parseInt(req.query.limit) || 20;
  const offset = parseInt(req.query.offset) || 0;
  
  const paginatedPosts = posts
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    .slice(offset, offset + limit);
  
  res.json({
    posts: paginatedPosts,
    total: posts.length,
    limit,
    offset
  });
});

// GET /api/posts/:id - Obtener un post específico
app.get('/api/posts/:id', (req, res) => {
  const post = posts.find(p => p.id === req.params.id);
  if (!post) {
    return res.status(404).json({ error: 'Post not found' });
  }
  
  // Obtener comentarios del post
  const postComments = comments
    .filter(c => c.postId === req.params.id)
    .sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
  
  res.json({
    ...post,
    comments: postComments
  });
});

// POST /api/posts - Crear un nuevo post
app.post('/api/posts', (req, res) => {
  const user = getOrCreateUser(req);
  const { title, content } = req.body;
  
  if (!title || !content) {
    return res.status(400).json({ error: 'Title and content are required' });
  }
  
  const newPost = {
    id: uuidv4(),
    title: title.trim(),
    content: content.trim(),
    author: user.alias,
    authorId: user.id,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    votes: 0,
    commentCount: 0
  };
  
  posts.unshift(newPost);
  
  res.status(201).json(newPost);
});

// ===== COMMENTS ROUTES =====

// GET /api/posts/:postId/comments - Obtener comentarios de un post
app.get('/api/posts/:postId/comments', (req, res) => {
  const postComments = comments
    .filter(c => c.postId === req.params.postId)
    .sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
  
  res.json(postComments);
});

// POST /api/comments - Crear un comentario
app.post('/api/comments', (req, res) => {
  const user = getOrCreateUser(req);
  const { postId, content, parentId } = req.body;
  
  if (!postId || !content) {
    return res.status(400).json({ error: 'PostId and content are required' });
  }
  
  const post = posts.find(p => p.id === postId);
  if (!post) {
    return res.status(404).json({ error: 'Post not found' });
  }
  
  const newComment = {
    id: uuidv4(),
    postId,
    content: content.trim(),
    author: user.alias,
    authorId: user.id,
    parentId: parentId || null,
    createdAt: new Date().toISOString(),
    votes: 0,
    replies: []
  };
  
  comments.push(newComment);
  
  // Actualizar contador de comentarios del post
  post.commentCount = comments.filter(c => c.postId === postId).length;
  
  res.status(201).json(newComment);
});

// ===== VOTES ROUTES =====

// POST /api/posts/:id/vote - Votar en un post
app.post('/api/posts/:id/vote', (req, res) => {
  const user = getOrCreateUser(req);
  const { voteType } = req.body; // 'up' o 'down'
  const postId = req.params.id;
  
  const post = posts.find(p => p.id === postId);
  if (!post) {
    return res.status(404).json({ error: 'Post not found' });
  }
  
  const voteKey = `${user.id}-post-${postId}`;
  const existingVote = votes[voteKey];
  
  // Remover voto anterior si existe
  if (existingVote) {
    post.votes += existingVote === 'up' ? -1 : 1;
  }
  
  // Aplicar nuevo voto si es diferente
  if (!existingVote || existingVote !== voteType) {
    votes[voteKey] = voteType;
    post.votes += voteType === 'up' ? 1 : -1;
  } else {
    // Si es el mismo voto, removerlo
    delete votes[voteKey];
  }
  
  res.json({ votes: post.votes, userVote: votes[voteKey] || null });
});

// POST /api/comments/:id/vote - Votar en un comentario
app.post('/api/comments/:id/vote', (req, res) => {
  const user = getOrCreateUser(req);
  const { voteType } = req.body;
  const commentId = req.params.id;
  
  const comment = comments.find(c => c.id === commentId);
  if (!comment) {
    return res.status(404).json({ error: 'Comment not found' });
  }
  
  const voteKey = `${user.id}-comment-${commentId}`;
  const existingVote = votes[voteKey];
  
  if (existingVote) {
    comment.votes += existingVote === 'up' ? -1 : 1;
  }
  
  if (!existingVote || existingVote !== voteType) {
    votes[voteKey] = voteType;
    comment.votes += voteType === 'up' ? 1 : -1;
  } else {
    delete votes[voteKey];
  }
  
  res.json({ votes: comment.votes, userVote: votes[voteKey] || null });
});

// ===== USERS ROUTES =====

// POST /api/users - Registrar/actualizar usuario
app.post('/api/users', (req, res) => {
  const user = getOrCreateUser(req);
  res.json(user);
});

// GET /api/users/profile - Obtener perfil del usuario
app.get('/api/users/profile', (req, res) => {
  const user = getOrCreateUser(req);
  res.json(user);
});

// PUT /api/users/alias - Actualizar alias del usuario
app.put('/api/users/alias', (req, res) => {
  const user = getOrCreateUser(req);
  const { alias } = req.body;
  
  if (!alias || alias.trim().length < 3) {
    return res.status(400).json({ error: 'Alias must be at least 3 characters long' });
  }
  
  user.alias = alias.trim();
  res.json(user);
});

// Iniciar servidor
app.listen(PORT, () => {
  console.log(`🚀 Mini Reddit Dev Server running on http://localhost:${PORT}`);
  console.log(`📡 API available at http://localhost:${PORT}/api`);
  console.log(`🔍 Health check: http://localhost:${PORT}/api/health`);
});
