{"name": "mini-reddit-backend", "version": "1.0.0", "description": "Backend serverless para Mini Reddit con AWS Lambda + DynamoDB", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "node dev-server.js", "deploy": "node deploy.js", "setup-aws": "node setup-aws.js"}, "dependencies": {"aws-sdk": "^2.1498.0", "uuid": "^9.0.1", "express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"archiver": "^6.0.1"}, "keywords": ["aws", "lambda", "dynamodb", "serverless", "reddit"], "author": "Student Project", "license": "MIT"}