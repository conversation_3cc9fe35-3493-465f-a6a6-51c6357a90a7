/**
 * Servidor de desarrollo local para Mini Reddit
 * Simula el comportamiento del backend AWS
 */

const express = require('express')
const cors = require('cors')
const { v4: uuidv4 } = require('uuid')

const app = express()
const PORT = 3001

// Middleware
app.use(cors())
app.use(express.json())

// Base de datos en memoria para desarrollo
let posts = [
  {
    id: 'example-1',
    title: '¡Bienvenido a Mini Reddit!',
    content: 'Esta es una demostración de la red social tipo Reddit. Puedes crear posts, comentar y votar. Todo funciona con React + AWS Lambda + DynamoDB.\n\nEste es un post de ejemplo para mostrar cómo se ve el contenido.',
    authorAlias: 'AdminDemo',
    authorId: 'demo-user',
    createdAt: new Date().toISOString(),
    votes: 5,
    commentCount: 3,
    userVote: null
  },
  {
    id: 'example-2',
    title: 'Características principales del proyecto',
    content: '• Sistema de posts y comentarios anidados\n• Votos positivos y negativos\n• Alias de usuario basado en IP\n• Interfaz responsive con TailwindCSS\n• Backend serverless con AWS\n• Persistencia en DynamoDB',
    authorAlias: 'DevStudent',
    authorId: 'demo-user-2',
    createdAt: new Date(Date.now() - 3600000).toISOString(),
    votes: 12,
    commentCount: 7,
    userVote: null
  }
]

let comments = [
  {
    id: 'comment-1',
    postId: 'example-1',
    content: '¡Excelente proyecto! Me gusta mucho la interfaz.',
    authorAlias: 'UsuarioDemo1',
    authorId: 'demo-comment-user-1',
    createdAt: new Date(Date.now() - 1800000).toISOString(),
    votes: 3,
    userVote: null,
    parentId: null,
    level: 0,
    replies: []
  }
]

let votes = []
let users = []

// Rutas de Posts
app.get('/api/posts', (req, res) => {
  const sortedPosts = [...posts].sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
  res.json({
    posts: sortedPosts,
    total: posts.length
  })
})

app.get('/api/posts/:id', (req, res) => {
  const post = posts.find(p => p.id === req.params.id)
  if (!post) {
    return res.status(404).json({ error: 'Post not found' })
  }
  res.json(post)
})

app.post('/api/posts', (req, res) => {
  const { title, content, authorId, authorAlias, authorIP } = req.body
  
  const newPost = {
    id: uuidv4(),
    title,
    content: content || '',
    authorId,
    authorAlias,
    authorIP,
    votes: 0,
    commentCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  
  posts.push(newPost)
  res.status(201).json(newPost)
})

// Rutas de Comentarios
app.get('/api/posts/:postId/comments', (req, res) => {
  const postComments = comments.filter(c => c.postId === req.params.postId)
  res.json({
    comments: postComments,
    total: postComments.length
  })
})

app.post('/api/comments', (req, res) => {
  const { postId, content, authorId, authorAlias, authorIP } = req.body
  
  const newComment = {
    id: uuidv4(),
    postId,
    content,
    authorId,
    authorAlias,
    authorIP,
    parentId: null,
    level: 0,
    votes: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    replies: []
  }
  
  comments.push(newComment)
  
  // Incrementar contador en el post
  const post = posts.find(p => p.id === postId)
  if (post) {
    post.commentCount++
  }
  
  res.status(201).json(newComment)
})

// Rutas de Votos
app.post('/api/posts/:postId/vote', (req, res) => {
  const { voteType } = req.body
  const postId = req.params.postId
  const userId = req.headers['x-user-id']
  
  const post = posts.find(p => p.id === postId)
  if (!post) {
    return res.status(404).json({ error: 'Post not found' })
  }
  
  // Simular lógica de votos
  const existingVoteIndex = votes.findIndex(v => v.userId === userId && v.targetId === postId)
  
  if (existingVoteIndex >= 0) {
    const existingVote = votes[existingVoteIndex]
    if (voteType === null) {
      // Remover voto
      votes.splice(existingVoteIndex, 1)
      post.votes += existingVote.voteType === 'up' ? -1 : 1
    } else if (existingVote.voteType !== voteType) {
      // Cambiar voto
      votes[existingVoteIndex].voteType = voteType
      post.votes += voteType === 'up' ? 
        (existingVote.voteType === 'down' ? 2 : 1) : 
        (existingVote.voteType === 'up' ? -2 : -1)
    }
  } else if (voteType !== null) {
    // Nuevo voto
    votes.push({
      id: uuidv4(),
      userId,
      targetId: postId,
      targetType: 'post',
      voteType,
      createdAt: new Date().toISOString()
    })
    post.votes += voteType === 'up' ? 1 : -1
  }
  
  res.json({
    totalVotes: post.votes,
    userVote: voteType
  })
})

// Rutas de Usuarios
app.post('/api/users', (req, res) => {
  const { id, alias, ip } = req.body
  
  let user = users.find(u => u.id === id)
  
  if (user) {
    user.alias = alias
    user.ip = ip
    user.lastSeen = new Date().toISOString()
  } else {
    user = {
      id,
      alias,
      ip,
      lastSeen: new Date().toISOString(),
      postCount: 0,
      commentCount: 0,
      createdAt: new Date().toISOString()
    }
    users.push(user)
  }
  
  res.json(user)
})

app.get('/api/users/profile', (req, res) => {
  const userId = req.headers['x-user-id']
  let user = users.find(u => u.id === userId)
  
  if (!user) {
    user = {
      id: userId,
      alias: req.headers['x-user-alias'] || 'Usuario',
      ip: req.headers['x-user-ip'] || 'unknown',
      lastSeen: new Date().toISOString(),
      postCount: 0,
      commentCount: 0,
      createdAt: new Date().toISOString()
    }
    users.push(user)
  }
  
  res.json(user)
})

// Middleware de manejo de errores
app.use((err, req, res, next) => {
  console.error(err.stack)
  res.status(500).json({ error: 'Something went wrong!' })
})

// Iniciar servidor
app.listen(PORT, () => {
  console.log(`🚀 Servidor de desarrollo ejecutándose en http://localhost:${PORT}`)
  console.log(`📝 API disponible en http://localhost:${PORT}/api`)
  console.log(`🔧 Modo desarrollo - Datos en memoria`)
})

module.exports = app
