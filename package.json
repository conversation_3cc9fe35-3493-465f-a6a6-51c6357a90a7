{"name": "mini-reddit", "private": true, "version": "1.0.0", "type": "module", "description": "Red social tipo Reddit/Twitter con React + AWS Lambda + DynamoDB", "scripts": {"dev": "vite", "dev:full": "concurrently \"npm run dev:server\" \"npm run dev\"", "dev:server": "cd dev-server && npm install && npm start", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "install:complete": "node scripts/install.js", "deploy:s3": "npm run build && node scripts/deploy-s3.js", "deploy:amplify": "node scripts/deploy-amplify.js", "aws:setup": "node scripts/setup-aws.js", "setup:api": "node scripts/setup-api-gateway.js"}, "dependencies": {"axios": "^1.6.2", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "aws-sdk": "^2.1498.0", "dotenv": "^16.3.1", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "vite": "^7.0.5", "concurrently": "^8.2.2"}, "keywords": ["react", "aws", "lambda", "dynamodb", "reddit", "social-network"], "author": "Student Project", "license": "MIT"}