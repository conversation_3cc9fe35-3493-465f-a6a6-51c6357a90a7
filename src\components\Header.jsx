import { Link } from 'react-router-dom'
import { PlusCircle, Home, User } from 'lucide-react'
import { useUser } from '../contexts/UserContext'
import UserMenu from './UserMenu'

const Header = () => {
  const { user, loading } = useUser()
  const isDevelopment = !import.meta.env.VITE_API_BASE_URL || import.meta.env.VITE_API_BASE_URL.includes('localhost')

  return (
    <>
      {/* Banner de desarrollo */}
      {isDevelopment && (
        <div className="bg-yellow-100 border-b border-yellow-300 px-4 py-2 text-center">
          <p className="text-yellow-800 text-sm">
            🔧 <strong>Modo Desarrollo</strong> - Usando datos de ejemplo.
            <span className="ml-2">
              Para conectar con AWS, configura VITE_API_BASE_URL en .env
            </span>
          </p>
        </div>
      )}

      <header className="bg-white shadow-sm border-b border-reddit-gray-200 sticky top-0 z-50">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="flex items-center justify-between h-16">
          {/* Logo y navegación principal */}
          <div className="flex items-center space-x-6">
            <Link 
              to="/" 
              className="flex items-center space-x-2 text-reddit-orange font-bold text-xl hover:text-orange-600 transition-colors"
            >
              <div className="w-8 h-8 bg-reddit-orange rounded-full flex items-center justify-center text-white font-bold">
                R
              </div>
              <span>Mini Reddit</span>
            </Link>
            
            <nav className="hidden md:flex items-center space-x-4">
              <Link 
                to="/" 
                className="flex items-center space-x-1 text-reddit-gray-600 hover:text-reddit-blue transition-colors px-3 py-2 rounded-md hover:bg-reddit-gray-100"
              >
                <Home size={18} />
                <span>Inicio</span>
              </Link>
            </nav>
          </div>

          {/* Acciones del usuario */}
          <div className="flex items-center space-x-4">
            {/* Botón crear post */}
            <Link 
              to="/create" 
              className="btn-primary flex items-center space-x-2"
            >
              <PlusCircle size={18} />
              <span className="hidden sm:inline">Crear Post</span>
            </Link>

            {/* Información del usuario */}
            {loading ? (
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-reddit-gray-200 rounded-full animate-pulse"></div>
                <div className="w-20 h-4 bg-reddit-gray-200 rounded animate-pulse"></div>
              </div>
            ) : user ? (
              <UserMenu user={user} />
            ) : (
              <div className="flex items-center space-x-2 text-reddit-gray-500">
                <User size={18} />
                <span className="text-sm">Cargando...</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
    </>
  )
}

export default Header
