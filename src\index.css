@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply antialiased;
  }
}

@layer components {
  .btn-primary {
    @apply bg-reddit-blue text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors duration-200 font-medium;
  }
  
  .btn-secondary {
    @apply bg-reddit-gray-100 text-reddit-gray-700 px-4 py-2 rounded-md hover:bg-reddit-gray-200 transition-colors duration-200 font-medium;
  }
  
  .btn-upvote {
    @apply text-reddit-gray-400 hover:text-reddit-orange transition-colors duration-200;
  }
  
  .btn-upvote.active {
    @apply text-reddit-orange;
  }
  
  .btn-downvote {
    @apply text-reddit-gray-400 hover:text-blue-600 transition-colors duration-200;
  }
  
  .btn-downvote.active {
    @apply text-blue-600;
  }
  
  .post-card {
    @apply bg-white rounded-lg shadow-sm border border-reddit-gray-200 hover:shadow-md transition-shadow duration-200;
  }
  
  .comment-thread {
    @apply border-l-2 border-reddit-gray-200 pl-4 ml-4;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-reddit-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-reddit-blue focus:border-transparent;
  }
  
  .textarea-field {
  @apply w-full px-3 py-2 border border-reddit-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-reddit-blue focus:border-transparent min-h-[100px] resize;
  resize: vertical; /* Esta línea es CSS estándar */
}
}
