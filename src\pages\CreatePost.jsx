import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowLeft, Send } from 'lucide-react'
import { useUser } from '../contexts/UserContext'
import { postsAPI } from '../utils/api'

const CreatePost = () => {
  const navigate = useNavigate()
  const { user } = useUser()
  const [formData, setFormData] = useState({
    title: '',
    content: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!formData.title.trim()) {
      setError('El título es obligatorio')
      return
    }

    if (formData.title.length > 200) {
      setError('El título no puede tener más de 200 caracteres')
      return
    }

    if (formData.content.length > 5000) {
      setError('El contenido no puede tener más de 5000 caracteres')
      return
    }

    setIsSubmitting(true)
    setError('')

    try {
      const postData = {
        title: formData.title.trim(),
        content: formData.content.trim(),
        authorId: user.id,
        authorAlias: user.alias,
        authorIP: user.ip
      }

      const newPost = await postsAPI.create(postData)

      // Mostrar mensaje de éxito
      if (newPost.id.startsWith('post_')) {
        // Post simulado en desarrollo
        alert('✅ Post creado exitosamente (modo desarrollo)\n\nEn producción, este post se guardaría en DynamoDB.')
        navigate('/')
      } else {
        // Post real en producción
        navigate(`/post/${newPost.id}`)
      }
    } catch (error) {
      console.error('Error creating post:', error)
      if (error.isDevelopment) {
        setError('🔧 Modo desarrollo: Backend no disponible. El post se simula localmente.')
      } else {
        setError('Error al crear el post. Inténtalo de nuevo.')
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    if (formData.title || formData.content) {
      if (window.confirm('¿Estás seguro de que quieres cancelar? Se perderán los cambios.')) {
        navigate('/')
      }
    } else {
      navigate('/')
    }
  }

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="flex items-center space-x-4 mb-6">
        <button
          onClick={handleCancel}
          className="flex items-center space-x-2 text-reddit-gray-600 hover:text-reddit-blue transition-colors"
        >
          <ArrowLeft size={20} />
          <span>Volver</span>
        </button>
        <h1 className="text-2xl font-bold text-reddit-gray-800">
          Crear Nuevo Post
        </h1>
      </div>

      {/* Información del usuario */}
      <div className="bg-white rounded-lg p-4 mb-6 border border-reddit-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-reddit-blue rounded-full flex items-center justify-center text-white font-medium">
            {user?.alias?.charAt(0).toUpperCase()}
          </div>
          <div>
            <p className="font-medium text-reddit-gray-800">
              Publicando como <strong>{user?.alias}</strong>
            </p>
            <p className="text-sm text-reddit-gray-500">
              Tu identidad se basa en tu IP: {user?.ip}
            </p>
          </div>
        </div>
      </div>

      {/* Formulario */}
      <form onSubmit={handleSubmit} className="bg-white rounded-lg p-6 border border-reddit-gray-200">
        <div className="space-y-6">
          {/* Título */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-reddit-gray-700 mb-2">
              Título *
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              placeholder="Escribe un título llamativo para tu post..."
              className="input-field"
              maxLength={200}
              disabled={isSubmitting}
              required
            />
            <div className="flex justify-between items-center mt-1">
              <span className={`text-xs ${
                formData.title.length > 180 ? 'text-red-500' : 'text-reddit-gray-500'
              }`}>
                {formData.title.length}/200 caracteres
              </span>
            </div>
          </div>

          {/* Contenido */}
          <div>
            <label htmlFor="content" className="block text-sm font-medium text-reddit-gray-700 mb-2">
              Contenido (opcional)
            </label>
            <textarea
              id="content"
              name="content"
              value={formData.content}
              onChange={handleChange}
              placeholder="Escribe el contenido de tu post... Puedes usar saltos de línea para organizar mejor tu texto."
              className="textarea-field"
              rows={8}
              maxLength={5000}
              disabled={isSubmitting}
            />
            <div className="flex justify-between items-center mt-1">
              <span className="text-xs text-reddit-gray-500">
                Puedes dejar el contenido vacío si solo quieres hacer una pregunta en el título
              </span>
              <span className={`text-xs ${
                formData.content.length > 4500 ? 'text-red-500' : 'text-reddit-gray-500'
              }`}>
                {formData.content.length}/5000 caracteres
              </span>
            </div>
          </div>

          {/* Error */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}

          {/* Botones */}
          <div className="flex space-x-4 pt-4 border-t border-reddit-gray-200">
            <button
              type="submit"
              disabled={isSubmitting || !formData.title.trim()}
              className="btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Send size={18} />
              <span>{isSubmitting ? 'Publicando...' : 'Publicar Post'}</span>
            </button>
            
            <button
              type="button"
              onClick={handleCancel}
              disabled={isSubmitting}
              className="btn-secondary disabled:opacity-50"
            >
              Cancelar
            </button>
          </div>
        </div>
      </form>

      {/* Consejos */}
      <div className="mt-6 bg-reddit-gray-50 rounded-lg p-4">
        <h3 className="font-medium text-reddit-gray-800 mb-2">
          Consejos para un buen post:
        </h3>
        <ul className="text-sm text-reddit-gray-600 space-y-1">
          <li>• Usa un título claro y descriptivo</li>
          <li>• Organiza tu contenido con saltos de línea</li>
          <li>• Sé respetuoso con otros usuarios</li>
          <li>• Revisa tu ortografía antes de publicar</li>
        </ul>
      </div>
    </div>
  )
}

export default CreatePost
