import axios from 'axios'

// Configuración base de la API
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'

// Flag para desarrollo local
const IS_DEVELOPMENT = !import.meta.env.VITE_API_BASE_URL || import.meta.env.VITE_API_BASE_URL.includes('localhost')

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Interceptor para agregar información del usuario a las requests
api.interceptors.request.use((config) => {
  const user = JSON.parse(localStorage.getItem('mini-reddit-user') || '{}')
  if (user.id) {
    config.headers['X-User-ID'] = user.id
    config.headers['X-User-Alias'] = user.alias
    config.headers['X-User-IP'] = user.ip
  }
  return config
})

// Interceptor para manejo de errores
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (IS_DEVELOPMENT && error.code === 'ERR_NETWORK') {
      console.warn('🔧 Modo desarrollo: Backend no disponible, usando datos de ejemplo')
      // En desarrollo, no rechazar la promesa para que se usen datos de ejemplo
      return Promise.reject({ ...error, isDevelopment: true })
    }
    console.error('API Error:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// ===== POSTS API =====

export const postsAPI = {
  // Obtener todos los posts
  getAll: async (limit = 20, offset = 0) => {
    const response = await api.get(`/posts?limit=${limit}&offset=${offset}`)
    return response.data
  },

  // Obtener un post específico
  getById: async (id) => {
    const response = await api.get(`/posts/${id}`)
    return response.data
  },

  // Crear un nuevo post
  create: async (postData) => {
    try {
      const response = await api.post('/posts', postData)
      return response.data
    } catch (error) {
      if (IS_DEVELOPMENT && error.isDevelopment) {
        // Simular creación exitosa en desarrollo
        const mockPost = {
          id: `post_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          ...postData,
          votes: 0,
          commentCount: 0,
          createdAt: new Date().toISOString()
        }
        console.log('🔧 Post simulado creado:', mockPost)
        return mockPost
      }
      throw error
    }
  },

  // Actualizar un post
  update: async (id, postData) => {
    const response = await api.put(`/posts/${id}`, postData)
    return response.data
  },

  // Eliminar un post
  delete: async (id) => {
    const response = await api.delete(`/posts/${id}`)
    return response.data
  }
}

// ===== COMMENTS API =====

export const commentsAPI = {
  // Obtener comentarios de un post
  getByPostId: async (postId) => {
    const response = await api.get(`/posts/${postId}/comments`)
    return response.data
  },

  // Crear un comentario
  create: async (commentData) => {
    const response = await api.post('/comments', commentData)
    return response.data
  },

  // Responder a un comentario
  reply: async (parentId, commentData) => {
    const response = await api.post(`/comments/${parentId}/reply`, commentData)
    return response.data
  },

  // Actualizar un comentario
  update: async (id, commentData) => {
    const response = await api.put(`/comments/${id}`, commentData)
    return response.data
  },

  // Eliminar un comentario
  delete: async (id) => {
    const response = await api.delete(`/comments/${id}`)
    return response.data
  }
}

// ===== VOTES API =====

export const votesAPI = {
  // Votar en un post
  votePost: async (postId, voteType) => {
    const response = await api.post(`/posts/${postId}/vote`, { voteType })
    return response.data
  },

  // Votar en un comentario
  voteComment: async (commentId, voteType) => {
    const response = await api.post(`/comments/${commentId}/vote`, { voteType })
    return response.data
  },

  // Obtener votos del usuario
  getUserVotes: async () => {
    const response = await api.get('/votes/user')
    return response.data
  }
}

// ===== USERS API =====

export const usersAPI = {
  // Registrar/actualizar usuario
  register: async (userData) => {
    const response = await api.post('/users', userData)
    return response.data
  },

  // Obtener información del usuario
  getProfile: async () => {
    const response = await api.get('/users/profile')
    return response.data
  },

  // Actualizar alias del usuario
  updateAlias: async (newAlias) => {
    const response = await api.put('/users/alias', { alias: newAlias })
    return response.data
  }
}

export default api
